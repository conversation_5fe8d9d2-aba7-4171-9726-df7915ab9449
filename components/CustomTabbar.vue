<template>
    <u-tabbar
      :value="current"
      @change="onChange"
      :fixed="true"
      :safeAreaInsetBottom="true"
      activeColor="#093C6B"
      inactiveColor="#666"
      :border="false"
      :placeholder="true"
      :zIndex="999"
    >
      <u-tabbar-item icon="home" text="找工作" />
      <u-tabbar-item icon="account" text="找牛人" />
      <u-tabbar-item icon="bell" text="订阅通知" />
      <u-tabbar-item icon="chat" text="消息" />
      <u-tabbar-item icon="man" text="我的" />
    </u-tabbar>
  </template>
  
  <script>
  export default {
    name: 'CustomTabbar',
    props: {
      // 当前选中的tab索引
      value: {
        type: Number,
        default: 0
      }
    },
    computed: {
      current() {
        return this.value
      }
    },
    methods: {
      onChange(index) {
        if (index === this.value) return
        // 跳转到对应页面
        switch (index) {
          case 0:
            uni.switchTab({ url: '/pages/job/index' })
            break
          case 1:
            uni.switchTab({ url: '/pages/talent/index' })
            break
          case 2:
            uni.switchTab({ url: '/pages/subscribe/index' })
            break
          case 3:
            uni.switchTab({ url: '/pages/message/index' })
            break
          case 4:
            uni.switchTab({ url: '/pages/mine/index' })
            break
        }
      }
    }
  }
  </script>