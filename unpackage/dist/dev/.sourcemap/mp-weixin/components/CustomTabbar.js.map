{"version": 3, "sources": ["webpack:////Users/<USER>/Documents/项目管家/gerenxiangmu/zhinipomo/qianduan/zhinipomo/components/CustomTabbar.vue?953f", "uni-app:///components/CustomTabbar.vue", "webpack:////Users/<USER>/Documents/项目管家/gerenxiangmu/zhinipomo/qianduan/zhinipomo/components/CustomTabbar.vue?1b14", "webpack:////Users/<USER>/Documents/项目管家/gerenxiangmu/zhinipomo/qianduan/zhinipomo/components/CustomTabbar.vue?bdb0", "webpack:////Users/<USER>/Documents/项目管家/gerenxiangmu/zhinipomo/qianduan/zhinipomo/components/CustomTabbar.vue?bb12"], "names": ["name", "props", "value", "type", "default", "computed", "current", "mounted", "methods", "getTabbarHeight", "query", "tabbarManager", "onChange", "uni", "url"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAw2B,CAAgB,0yBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACqB53B;;;;;;;;;;;;;;;;;;;;;eAEA;EACAA;EACAC;IACA;IACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MACA;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;IACAC;MAAA;MACA;QACA;QACAC;UACA;YACA;YACA;YACAC;UACA;QACA;MACA;IACA;IAEAC;MACA;MACA;MACA;QACA;UACAC;YAAAC;UAAA;UACA;QACA;UACAD;YAAAC;UAAA;UACA;QACA;UACAD;YAAAC;UAAA;UACA;QACA;UACAD;YAAAC;UAAA;UACA;QACA;UACAD;YAAAC;UAAA;UACA;MAAA;IAEA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC9EA;AAAA;AAAA;AAAA;AAAA;AAAyH;AACzH;AACgE;AACL;;;AAG3D;AACsN;AACtN,gBAAgB,iNAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,uFAAM;AACR,EAAE,gGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,2FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACtBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,aAAa,+TAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "components/CustomTabbar.js", "sourcesContent": ["import mod from \"-!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./CustomTabbar.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./CustomTabbar.vue?vue&type=script&lang=js&\"", "<template>\n    <u-tabbar\n      :value=\"current\"\n      @change=\"onChange\"\n      :fixed=\"true\"\n      :safeAreaInsetBottom=\"true\"\n      activeColor=\"#093C6B\"\n      inactiveColor=\"#666\"\n      :border=\"false\"\n      :placeholder=\"true\"\n      :zIndex=\"999\"\n    >\n      <u-tabbar-item icon=\"home\" text=\"找工作\" />\n      <u-tabbar-item icon=\"account\" text=\"找牛人\" />\n      <u-tabbar-item icon=\"bell\" text=\"订阅通知\" />\n      <u-tabbar-item icon=\"chat\" text=\"消息\" />\n      <u-tabbar-item icon=\"man\" text=\"我的\" />\n    </u-tabbar>\n  </template>\n  \n  <script>\n  import tabbarManager from '@/utils/tabbar.js'\n\n  export default {\n    name: 'CustomTabbar',\n    props: {\n      // 当前选中的tab索引\n      value: {\n        type: Number,\n        default: 0\n      }\n    },\n    computed: {\n      current() {\n        return this.value\n      }\n    },\n    mounted() {\n      // 组件挂载后获取tabbar高度\n      this.getTabbarHeight()\n    },\n    methods: {\n      // 获取tabbar高度并存储到全局\n      getTabbarHeight() {\n        this.$nextTick(() => {\n          const query = uni.createSelectorQuery().in(this)\n          query.select('.u-tabbar').boundingClientRect((data) => {\n            if (data) {\n              const height = data.height\n              // 使用TabbarManager设置高度\n              tabbarManager.setHeight(height)\n            }\n          }).exec()\n        })\n      },\n\n      onChange(index) {\n        if (index === this.value) return\n        // 跳转到对应页面\n        switch (index) {\n          case 0:\n            uni.switchTab({ url: '/pages/job/index' })\n            break\n          case 1:\n            uni.switchTab({ url: '/pages/talent/index' })\n            break\n          case 2:\n            uni.switchTab({ url: '/pages/subscribe/index' })\n            break\n          case 3:\n            uni.switchTab({ url: '/pages/message/index' })\n            break\n          case 4:\n            uni.switchTab({ url: '/pages/mine/index' })\n            break\n        }\n      }\n    }\n  }\n  </script>", "import { render, staticRenderFns, recyclableRender, components } from \"./CustomTabbar.vue?vue&type=template&id=7ba212ec&\"\nvar renderjs\nimport script from \"./CustomTabbar.vue?vue&type=script&lang=js&\"\nexport * from \"./CustomTabbar.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/CustomTabbar.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./CustomTabbar.vue?vue&type=template&id=7ba212ec&\"", "var components\ntry {\n  components = {\n    uTabbar: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-tabbar/u-tabbar\" */ \"uview-ui/components/u-tabbar/u-tabbar.vue\"\n      )\n    },\n    uTabbarItem: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-tabbar-item/u-tabbar-item\" */ \"uview-ui/components/u-tabbar-item/u-tabbar-item.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }"], "sourceRoot": ""}