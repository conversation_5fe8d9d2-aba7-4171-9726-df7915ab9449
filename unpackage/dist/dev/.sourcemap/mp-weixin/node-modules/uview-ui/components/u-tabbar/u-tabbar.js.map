{"version": 3, "sources": ["webpack:////Users/<USER>/Documents/项目管家/gerenxiangmu/zhinipomo/qianduan/zhinipomo/node_modules/uview-ui/components/u-tabbar/u-tabbar.vue?db21", "webpack:////Users/<USER>/Documents/项目管家/gerenxiangmu/zhinipomo/qianduan/zhinipomo/node_modules/uview-ui/components/u-tabbar/u-tabbar.vue?a275", "webpack:////Users/<USER>/Documents/项目管家/gerenxiangmu/zhinipomo/qianduan/zhinipomo/node_modules/uview-ui/components/u-tabbar/u-tabbar.vue?f542", "webpack:////Users/<USER>/Documents/项目管家/gerenxiangmu/zhinipomo/qianduan/zhinipomo/node_modules/uview-ui/components/u-tabbar/u-tabbar.vue?5059", "uni-app:///node_modules/uview-ui/components/u-tabbar/u-tabbar.vue", "webpack:////Users/<USER>/Documents/项目管家/gerenxiangmu/zhinipomo/qianduan/zhinipomo/node_modules/uview-ui/components/u-tabbar/u-tabbar.vue?e708", "webpack:////Users/<USER>/Documents/项目管家/gerenxiangmu/zhinipomo/qianduan/zhinipomo/node_modules/uview-ui/components/u-tabbar/u-tabbar.vue?7be0"], "names": ["name", "mixins", "data", "placeholder<PERSON><PERSON><PERSON>", "computed", "tabbarStyle", "zIndex", "update<PERSON>hild", "updatePlaceholder", "watch", "created", "mounted", "methods", "update<PERSON><PERSON><PERSON>n", "setPlaceholderHeight", "uni", "height"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACsC;;;AAG7F;AAC+N;AAC/N,gBAAgB,iNAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,+TAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1CA;AAAA;AAAA;AAAA;AAAi5B,CAAgB,syBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACyBr6B;;;;;;;;;;;;;;;;;;;;;;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAfA,eAgBA;EACAA;EACAC;EACAC;IACA;MACAC;IACA;EACA;EACAC;IACAC;MACA;QACAC;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;EACA;EACAC;IACAF;MACA;MACA;IACA;IACAC;MACA;MACA;IACA;EACA;EACAE;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACA;QAAA;MAAA;IACA;IACA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA,OAEAC;cAAA;gBAEA;kBAAA;oBAAAC;kBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAWA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AChHA;AAAA;AAAA;AAAA;AAAosD,CAAgB,67CAAG,EAAC,C;;;;;;;;;;;ACAxtD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "node-modules/uview-ui/components/u-tabbar/u-tabbar.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-tabbar.vue?vue&type=template&id=3426a5b2&scoped=true&\"\nvar renderjs\nimport script from \"./u-tabbar.vue?vue&type=script&lang=js&\"\nexport * from \"./u-tabbar.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-tabbar.vue?vue&type=style&index=0&id=3426a5b2&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"3426a5b2\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"node_modules/uview-ui/components/u-tabbar/u-tabbar.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-tabbar.vue?vue&type=template&id=3426a5b2&scoped=true&\"", "var components\ntry {\n  components = {\n    uSafeBottom: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-safe-bottom/u-safe-bottom\" */ \"uview-ui/components/u-safe-bottom/u-safe-bottom.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.__get_style([_vm.tabbarStyle])\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-tabbar.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-tabbar.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"u-tabbar\">\n\t\t<view\n\t\t    class=\"u-tabbar__content\"\n\t\t    ref=\"u-tabbar__content\"\n\t\t    @touchmove.stop.prevent=\"noop\"\n\t\t    :class=\"[border && 'u-border-top', fixed && 'u-tabbar--fixed']\"\n\t\t    :style=\"[tabbarStyle]\"\n\t\t>\n\t\t\t<view class=\"u-tabbar__content__item-wrapper\">\n\t\t\t\t<slot />\n\t\t\t</view>\n\t\t\t<u-safe-bottom v-if=\"safeAreaInsetBottom\"></u-safe-bottom>\n\t\t</view>\n\t\t<view\n\t\t    class=\"u-tabbar__placeholder\"\n\t\t\tv-if=\"placeholder\"\n\t\t    :style=\"{\n\t\t\t\theight: placeholderHeight + 'px',\n\t\t\t}\"\n\t\t></view>\n\t</view>\n</template>\n\n<script>\n\timport props from './props.js';\n\t// #ifdef APP-NVUE\n\tconst dom = uni.requireNativePlugin('dom')\n\t// #endif\n\t/**\n\t * Tabbar 底部导航栏\n\t * @description 此组件提供了自定义tabbar的能力。\n\t * @tutorial https://www.uviewui.com/components/tabbar.html\n\t * @property {String | Number}\tvalue\t\t\t\t当前匹配项的name\n\t * @property {Boolean}\t\t\tsafeAreaInsetBottom\t是否为iPhoneX留出底部安全距离（默认 true ）\n\t * @property {Boolean}\t\t\tborder\t\t\t\t是否显示上方边框（默认 true ）\n\t * @property {String | Number}\tzIndex\t\t\t\t元素层级z-index（默认 1 ）\n\t * @property {String}\t\t\tactiveColor\t\t\t选中标签的颜色（默认 '#1989fa' ）\n\t * @property {String}\t\t\tinactiveColor\t\t未选中标签的颜色（默认 '#7d7e80' ）\n\t * @property {Boolean}\t\t\tfixed\t\t\t\t是否固定在底部（默认 true ）\n\t * @property {Boolean}\t\t\tplaceholder\t\t\tfixed定位固定在底部时，是否生成一个等高元素防止塌陷（默认 true ）\n\t * @property {Object}\t\t\tcustomStyle\t\t\t定义需要用到的外部样式\n\t * \n\t * @example <u-tabbar :value=\"value2\" :placeholder=\"false\" @change=\"name => value2 = name\" :fixed=\"false\" :safeAreaInsetBottom=\"false\"><u-tabbar-item text=\"首页\" icon=\"home\" dot ></u-tabbar-item></u-tabbar>\n\t */\n\texport default {\n\t\tname: 'u-tabbar',\n\t\tmixins: [uni.$u.mpMixin, uni.$u.mixin,props],\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tplaceholderHeight: 0\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\t\t\ttabbarStyle() {\n\t\t\t\tconst style = {\n\t\t\t\t\tzIndex: this.zIndex\n\t\t\t\t}\n\t\t\t\t// 合并来自父组件的customStyle样式\n\t\t\t\treturn uni.$u.deepMerge(style, uni.$u.addStyle(this.customStyle))\n\t\t\t},\n\t\t\t// 监听多个参数的变化，通过在computed执行对应的操作\n\t\t\tupdateChild() {\n\t\t\t\treturn [this.value, this.activeColor, this.inactiveColor]\n\t\t\t},\n\t\t\tupdatePlaceholder() {\n\t\t\t\treturn [this.fixed, this.placeholder]\n\t\t\t}\n\t\t},\n\t\twatch: {\n\t\t\tupdateChild() {\n\t\t\t\t// 如果updateChildren中的元素发生了变化，则执行子元素初始化操作\n\t\t\t\tthis.updateChildren()\n\t\t\t},\n\t\t\tupdatePlaceholder() {\n\t\t\t\t// 如果fixed，placeholder等参数发生变化，重新计算占位元素的高度\n\t\t\t\tthis.setPlaceholderHeight()\n\t\t\t}\n\t\t},\n\t\tcreated() {\n\t\t\tthis.children = []\n\t\t},\n\t\tmounted() {\n\t\t\tthis.setPlaceholderHeight()\n\t\t},\n\t\tmethods: {\n\t\t\tupdateChildren() {\n\t\t\t\t// 如果存在子元素，则执行子元素的updateFromParent进行更新数据\n\t\t\t\tthis.children.length && this.children.map(child => child.updateFromParent())\n\t\t\t},\n\t\t\t// 设置用于防止塌陷元素的高度\n\t\t\tasync setPlaceholderHeight() {\n\t\t\t\tif (!this.fixed || !this.placeholder) return\n\t\t\t\t// 延时一定时间\n\t\t\t\tawait uni.$u.sleep(20)\n\t\t\t\t// #ifndef APP-NVUE\n\t\t\t\tthis.$uGetRect('.u-tabbar__content').then(({height = 50}) => {\n\t\t\t\t\t// 修复IOS safearea bottom 未填充高度\n\t\t\t\t\tthis.placeholderHeight = height\n\t\t\t\t})\n\t\t\t\t// #endif\n\n\t\t\t\t// #ifdef APP-NVUE\n\t\t\t\tdom.getComponentRect(this.$refs['u-tabbar__content'], (res) => {\n\t\t\t\t\tconst {\n\t\t\t\t\t\tsize\n\t\t\t\t\t} = res\n\t\t\t\t\tthis.placeholderHeight = size.height\n\t\t\t\t})\n\t\t\t\t// #endif\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\" scoped>\n\t@import \"../../libs/css/components.scss\";\n\n\t.u-tabbar {\n\t\t@include flex(column);\n\t\tflex: 1;\n\t\tjustify-content: center;\n\t\t\n\t\t&__content {\n\t\t\t@include flex(column);\n\t\t\tbackground-color: #fff;\n\t\t\t\n\t\t\t&__item-wrapper {\n\t\t\t\theight: 50px;\n\t\t\t\t@include flex(row);\n\t\t\t}\n\t\t}\n\n\t\t&--fixed {\n\t\t\tposition: fixed;\n\t\t\tbottom: 0;\n\t\t\tleft: 0;\n\t\t\tright: 0;\n\t\t}\n\t}\n</style>\n", "import mod from \"-!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-tabbar.vue?vue&type=style&index=0&id=3426a5b2&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-tabbar.vue?vue&type=style&index=0&id=3426a5b2&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753615549356\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}