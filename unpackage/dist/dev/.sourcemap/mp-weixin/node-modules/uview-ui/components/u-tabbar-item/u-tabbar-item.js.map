{"version": 3, "sources": ["webpack:////Users/<USER>/Documents/项目管家/gerenxiangmu/zhinipomo/qianduan/zhinipomo/node_modules/uview-ui/components/u-tabbar-item/u-tabbar-item.vue?8117", "webpack:////Users/<USER>/Documents/项目管家/gerenxiangmu/zhinipomo/qianduan/zhinipomo/node_modules/uview-ui/components/u-tabbar-item/u-tabbar-item.vue?d641", "webpack:////Users/<USER>/Documents/项目管家/gerenxiangmu/zhinipomo/qianduan/zhinipomo/node_modules/uview-ui/components/u-tabbar-item/u-tabbar-item.vue?b6ed", "webpack:////Users/<USER>/Documents/项目管家/gerenxiangmu/zhinipomo/qianduan/zhinipomo/node_modules/uview-ui/components/u-tabbar-item/u-tabbar-item.vue?9523", "uni-app:///node_modules/uview-ui/components/u-tabbar-item/u-tabbar-item.vue", "webpack:////Users/<USER>/Documents/项目管家/gerenxiangmu/zhinipomo/qianduan/zhinipomo/node_modules/uview-ui/components/u-tabbar-item/u-tabbar-item.vue?b367", "webpack:////Users/<USER>/Documents/项目管家/gerenxiangmu/zhinipomo/qianduan/zhinipomo/node_modules/uview-ui/components/u-tabbar-item/u-tabbar-item.vue?0978"], "names": ["name", "mixins", "data", "isActive", "parentData", "value", "activeColor", "inactiveColor", "created", "methods", "init", "uni", "updateParentData", "updateFromParent", "clickHandler"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsI;AACtI;AACiE;AACL;AACsC;;;AAGlG;AAC+N;AAC/N,gBAAgB,iNAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,oGAAM;AACR,EAAE,6GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qRAEN;AACP,KAAK;AACL;AACA,aAAa,2RAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC/CA;AAAA;AAAA;AAAA;AAAs5B,CAAgB,2yBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;AC6C16B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAbA,eAcA;EACAA;EACAC;EACAC;IACA;MACAC;MAAA;MACAC;QACAC;QACAC;QACAC;MACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACA;MACA;QACAC;MACA;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MAAA;MACA;QACA;QACA;QACA;QACA;UACA;QACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC7GA;AAAA;AAAA;AAAA;AAAysD,CAAgB,k8CAAG,EAAC,C;;;;;;;;;;;ACA7tD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "node-modules/uview-ui/components/u-tabbar-item/u-tabbar-item.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-tabbar-item.vue?vue&type=template&id=b8fe2b06&scoped=true&\"\nvar renderjs\nimport script from \"./u-tabbar-item.vue?vue&type=script&lang=js&\"\nexport * from \"./u-tabbar-item.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-tabbar-item.vue?vue&type=style&index=0&id=b8fe2b06&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"b8fe2b06\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"node_modules/uview-ui/components/u-tabbar-item/u-tabbar-item.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-tabbar-item.vue?vue&type=template&id=b8fe2b06&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-icon/u-icon\" */ \"uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uBadge: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-badge/u-badge\" */ \"uview-ui/components/u-badge/u-badge.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.__get_style([_vm.$u.addStyle(_vm.customStyle)])\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-tabbar-item.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-tabbar-item.vue?vue&type=script&lang=js&\"", "<template>\n\t<view\n\t    class=\"u-tabbar-item\"\n\t    :style=\"[$u.addStyle(customStyle)]\"\n\t    @tap=\"clickHandler\"\n\t>\n\t\t<view class=\"u-tabbar-item__icon\">\n\t\t\t<u-icon\n\t\t\t    v-if=\"icon\"\n\t\t\t    :name=\"icon\"\n\t\t\t    :color=\"isActive? parentData.activeColor : parentData.inactiveColor\"\n\t\t\t    :size=\"20\"\n\t\t\t></u-icon>\n\t\t\t<template v-else>\n\t\t\t\t<slot\n\t\t\t\t    v-if=\"isActive\"\n\t\t\t\t    name=\"active-icon\"\n\t\t\t\t/>\n\t\t\t\t<slot\n\t\t\t\t    v-else\n\t\t\t\t    name=\"inactive-icon\"\n\t\t\t\t/>\n\t\t\t</template>\n\t\t\t<u-badge\n\t\t\t\tabsolute\n\t\t\t\t:offset=\"[0, dot ? '34rpx' : badge > 9 ? '14rpx' : '20rpx']\"\n\t\t\t    :customStyle=\"badgeStyle\"\n\t\t\t    :isDot=\"dot\"\n\t\t\t    :value=\"badge || (dot ? 1 : null)\"\n\t\t\t    :show=\"dot || badge > 0\"\n\t\t\t></u-badge>\n\t\t</view>\n\t\t\n\t\t<slot name=\"text\">\n\t\t\t<text\n\t\t\t    class=\"u-tabbar-item__text\"\n\t\t\t    :style=\"{\n\t\t\t\t\tcolor: isActive? parentData.activeColor : parentData.inactiveColor\n\t\t\t\t}\"\n\t\t\t>{{ text }}</text>\n\t\t</slot>\n\t</view>\n</template>\n\n<script>\n\timport props from './props.js';\n\t/**\n\t * TabbarItem 底部导航栏子组件\n\t * @description 此组件提供了自定义tabbar的能力。\n\t * @tutorial https://www.uviewui.com/components/tabbar.html\n\t * @property {String | Number}\tname\t\titem标签的名称，作为与u-tabbar的value参数匹配的标识符\n\t * @property {String}\t\t\ticon\t\tuView内置图标或者绝对路径的图片\n\t * @property {String | Number}\tbadge\t\t右上角的角标提示信息\n\t * @property {Boolean}\t\t\tdot\t\t\t是否显示圆点，将会覆盖badge参数（默认 false ）\n\t * @property {String}\t\t\ttext\t\t描述文本\n\t * @property {Object | String}\tbadgeStyle\t控制徽标的位置，对象或者字符串形式，可以设置top和right属性（默认 'top: 6px;right:2px;' ）\n\t * @property {Object}\t\t\tcustomStyle\t定义需要用到的外部样式\n\t * \n\t * @example <u-tabbar :value=\"value2\" :placeholder=\"false\" @change=\"name => value2 = name\" :fixed=\"false\" :safeAreaInsetBottom=\"false\"><u-tabbar-item text=\"首页\" icon=\"home\" dot ></u-tabbar-item></u-tabbar>\n\t */\n\texport default {\n\t\tname: 'u-tabbar-item',\n\t\tmixins: [uni.$u.mpMixin, uni.$u.mixin,props],\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tisActive: false, // 是否处于激活状态\n\t\t\t\tparentData: {\n\t\t\t\t\tvalue: null,\n\t\t\t\t\tactiveColor: '',\n\t\t\t\t\tinactiveColor: ''\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\tcreated() {\n\t\t\tthis.init()\n\t\t},\n\t\tmethods: {\n\t\t\tinit() {\n\t\t\t\t// 支付宝小程序不支持provide/inject，所以使用这个方法获取整个父组件，在created定义，避免循环引用\n\t\t\t\tthis.updateParentData()\n\t\t\t\tif (!this.parent) {\n\t\t\t\t\tuni.$u.error('u-tabbar-item必须搭配u-tabbar组件使用')\n\t\t\t\t}\n\t\t\t\t// 本子组件在u-tabbar的children数组中的索引\n\t\t\t\tconst index = this.parent.children.indexOf(this)\n\t\t\t\t// 判断本组件的name(如果没有定义name，就用index索引)是否等于父组件的value参数\n\t\t\t\tthis.isActive = (this.name || index) === this.parentData.value\n\t\t\t},\n\t\t\tupdateParentData() {\n\t\t\t\t// 此方法在mixin中\n\t\t\t\tthis.getParentData('u-tabbar')\n\t\t\t},\n\t\t\t// 此方法将会被父组件u-tabbar调用\n\t\t\tupdateFromParent() {\n\t\t\t\t// 重新初始化\n\t\t\t\tthis.init()\n\t\t\t},\n\t\t\tclickHandler() {\n\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\tconst index = this.parent.children.indexOf(this)\n\t\t\t\t\tconst name = this.name || index\n\t\t\t\t\t// 点击的item为非激活的item才发出change事件\n\t\t\t\t\tif (name !== this.parent.value) {\n\t\t\t\t\t\tthis.parent.$emit('change', name)\n\t\t\t\t\t}\n\t\t\t\t\tthis.$emit('click', name)\n\t\t\t\t})\n\t\t\t}\n\t\t},\n\t}\n</script>\n\n<style lang=\"scss\" scoped>\n\t@import \"../../libs/css/components.scss\";\n\n\t.u-tabbar-item {\n\t\t@include flex(column);\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tflex: 1;\n\t\t\n\t\t&__icon {\n\t\t\t@include flex;\n\t\t\tposition: relative;\n\t\t\twidth: 150rpx;\n\t\t\tjustify-content: center;\n\t\t}\n\n\t\t&__text {\n\t\t\tmargin-top: 2px;\n\t\t\tfont-size: 12px;\n\t\t\tcolor: $u-content-color;\n\t\t}\n\t}\n\n\t/* #ifdef MP */\n\t// 由于小程序都使用shadow DOM形式实现，需要给影子宿主设置flex: 1才能让其撑开\n\t:host {\n\t\tflex: 1\n\t}\n\t/* #endif */\n</style>\n", "import mod from \"-!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-tabbar-item.vue?vue&type=style&index=0&id=b8fe2b06&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-tabbar-item.vue?vue&type=style&index=0&id=b8fe2b06&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753615549318\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}