{"version": 3, "sources": ["webpack:////Users/<USER>/Documents/项目管家/gerenxiangmu/zhinipomo/qianduan/zhinipomo/node_modules/uview-ui/components/u--text/u--text.vue?f085", "webpack:////Users/<USER>/Documents/项目管家/gerenxiangmu/zhinipomo/qianduan/zhinipomo/node_modules/uview-ui/components/u--text/u--text.vue?a0c4", "webpack:////Users/<USER>/Documents/项目管家/gerenxiangmu/zhinipomo/qianduan/zhinipomo/node_modules/uview-ui/components/u--text/u--text.vue?6bc1", "webpack:////Users/<USER>/Documents/项目管家/gerenxiangmu/zhinipomo/qianduan/zhinipomo/node_modules/uview-ui/components/u--text/u--text.vue?4401", "uni-app:///node_modules/uview-ui/components/u--text/u--text.vue"], "names": ["name", "mixins", "components", "uvText"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;;;AAGtD;AAC+N;AAC/N,gBAAgB,iNAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,iFAAM;AACR,EAAE,0FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACtBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAg5B,CAAgB,oyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACmCp6B;AAAA;EAAA;IAAA;EAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AA3BA,eA4BA;EACAA;EACAC;EACAC;IACAC;EACA;AACA;AAAA,2B", "file": "node-modules/uview-ui/components/u--text/u--text.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u--text.vue?vue&type=template&id=1a602e2d&\"\nvar renderjs\nimport script from \"./u--text.vue?vue&type=script&lang=js&\"\nexport * from \"./u--text.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"node_modules/uview-ui/components/u--text/u--text.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u--text.vue?vue&type=template&id=1a602e2d&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u--text.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u--text.vue?vue&type=script&lang=js&\"", "<template>\r\n    <uvText\r\n        :type=\"type\"\r\n        :show=\"show\"\r\n        :text=\"text\"\r\n        :prefixIcon=\"prefixIcon\"\r\n        :suffixIcon=\"suffixIcon\"\r\n        :mode=\"mode\"\r\n        :href=\"href\"\r\n        :format=\"format\"\r\n        :call=\"call\"\r\n        :openType=\"openType\"\r\n        :bold=\"bold\"\r\n        :block=\"block\"\r\n        :lines=\"lines\"\r\n        :color=\"color\"\r\n\t\t:decoration=\"decoration\"\r\n        :size=\"size\"\r\n        :iconStyle=\"iconStyle\"\r\n        :margin=\"margin\"\r\n        :lineHeight=\"lineHeight\"\r\n        :align=\"align\"\r\n        :wordWrap=\"wordWrap\"\r\n        :customStyle=\"customStyle\"\r\n        @click=\"$emit('click')\"\r\n    ></uvText>\r\n</template>\r\n\r\n<script>\r\n/**\r\n * 此组件存在的理由是，在nvue下，u-text被uni-app官方占用了，u-text在nvue中相当于input组件\r\n * 所以在nvue下，取名为u--input，内部其实还是u-text.vue，只不过做一层中转\r\n * 不使用v-bind=\"$attrs\"，而是分开独立写传参，是因为微信小程序不支持此写法\r\n */\r\nimport uvText from \"../u-text/u-text.vue\";\r\nimport props from \"../u-text/props.js\";\r\n/**\r\n * Text 文本\r\n * @description 此组件集成了文本类在项目中的常用功能，包括状态，拨打电话，格式化日期，*替换，超链接...等功能。 您大可不必在使用特殊文本时自己定义，text组件几乎涵盖您能使用的大部分场景。\r\n * @tutorial https://www.uviewui.com/components/loading.html\r\n * @property {String} \t\t\t\t\ttype\t\t主题颜色\r\n * @property {Boolean} \t\t\t\t\tshow\t\t是否显示（默认 true ）\r\n * @property {String | Number}\t\t\ttext\t\t显示的值\r\n * @property {String}\t\t\t\t\tprefixIcon\t前置图标\r\n * @property {String} \t\t\t\t\tsuffixIcon\t后置图标\r\n * @property {String} \t\t\t\t\tmode\t\t文本处理的匹配模式 text-普通文本，price-价格，phone-手机号，name-姓名，date-日期，link-超链接\r\n * @property {String} \t\t\t\t\thref\t\tmode=link下，配置的链接\r\n * @property {String | Function} \t\tformat\t\t格式化规则\r\n * @property {Boolean} \t\t\t\t\tcall\t\tmode=phone时，点击文本是否拨打电话（默认 false ）\r\n * @property {String} \t\t\t\t\topenType\t小程序的打开方式\r\n * @property {Boolean} \t\t\t\t\tbold\t\t是否粗体，默认normal（默认 false ）\r\n * @property {Boolean} \t\t\t\t\tblock\t\t是否块状（默认 false ）\r\n * @property {String | Number} \t\t\tlines\t\t文本显示的行数，如果设置，超出此行数，将会显示省略号\r\n * @property {String} \t\t\t\t\tcolor\t\t文本颜色（默认 '#303133' ）\r\n * @property {String | Number} \t\t\tsize\t\t字体大小（默认 15 ）\r\n * @property {Object | String} \t\t\ticonStyle\t图标的样式 （默认 {fontSize: '15px'} ）\r\n * @property {String} \t\t\t\t\tdecoration\t文字装饰，下划线，中划线等，可选值 none|underline|line-through（默认 'none' ）\r\n * @property {Object | String | Number}\tmargin\t\t外边距，对象、字符串，数值形式均可（默认 0 ）\r\n * @property {String | Number} \t\t\tlineHeight\t文本行高\r\n * @property {String} \t\t\t\t\talign\t\t文本对齐方式，可选值left|center|right（默认 'left' ）\r\n * @property {String} \t\t\t\t\twordWrap\t文字换行，可选值break-word|normal|anywhere（默认 'normal' ）\r\n * @event {Function} click  点击触发事件\r\n * @example <u--text text=\"我用十年青春,赴你最后之约\"></u--text>\r\n */\r\nexport default {\r\n    name: \"u--text\",\r\n    mixins: [uni.$u.mpMixin, props, uni.$u.mixin],\r\n    components: {\r\n        uvText,\r\n    },\r\n};\r\n</script>\r\n"], "sourceRoot": ""}