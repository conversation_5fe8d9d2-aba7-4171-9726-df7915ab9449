@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* uni.scss */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.page.data-v-57280228 {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
.content.data-v-57280228 {
  padding: 40rpx 30rpx;
}
.content .header.data-v-57280228 {
  text-align: center;
  margin-bottom: 60rpx;
}
.content .header .title.data-v-57280228 {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 20rpx;
}
.content .header .subtitle.data-v-57280228 {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}
.content .info-section.data-v-57280228 {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 40rpx;
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
}
.content .info-section .info-item.data-v-57280228 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.content .info-section .info-item.data-v-57280228:last-child {
  margin-bottom: 0;
}
.content .info-section .info-item .label.data-v-57280228 {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}
.content .info-section .info-item .value.data-v-57280228 {
  font-size: 28rpx;
  color: white;
  font-weight: bold;
}
.content .demo-content.data-v-57280228 {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  padding: 40rpx;
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
}
.content .demo-content .demo-text.data-v-57280228 {
  font-size: 30rpx;
  color: white;
  text-align: center;
  line-height: 1.6;
}
