<template>
  <view class="page">
    <view class="content" :style="{ paddingBottom: tabbarHeight + 'px' }">
      <view class="demo-section">
        <text class="section-title">直接使用uni.createSelectorQuery()获取Tabbar高度</text>
        
        <view class="info-cards">
          <view class="info-card">
            <text class="card-label">当前Tabbar高度</text>
            <text class="card-value">{{ tabbarHeight }}px</text>
          </view>
          
          <view class="info-card">
            <text class="card-label">内容区域高度</text>
            <text class="card-value">{{ contentHeight }}px</text>
          </view>
          
          <view class="info-card">
            <text class="card-label">状态栏高度</text>
            <text class="card-value">{{ systemHeights.statusBarHeight }}px</text>
          </view>
        </view>
        
        <view class="buttons">
          <u-button type="primary" @click="getHeightDirectly">直接获取高度</u-button>
          <u-button type="success" @click="getHeightFromGlobal">从全局获取</u-button>
          <u-button type="warning" @click="measureCustomElement">测量自定义元素</u-button>
        </view>
        
        <view class="test-element" ref="testElement">
          <text class="test-text">这是一个测试元素，点击"测量自定义元素"按钮可以获取它的高度</text>
        </view>
        
        <view class="usage-tips">
          <text class="tips-title">使用方法：</text>
          <text class="tips-item">1. 在页面中直接调用 this.$getTabbarHeight() 获取高度</text>
          <text class="tips-item">2. 使用 this.$waitForTabbarHeight() 等待高度获取完成</text>
          <text class="tips-item">3. 通过 uni.tabbarHeight 全局访问</text>
          <text class="tips-item">4. 使用 this.$getElementHeight() 测量任意元素</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'SimpleUsage',
  data() {
    return {
      tabbarHeight: 0,
      contentHeight: 0,
      systemHeights: {},
      testElementHeight: 0
    }
  },
  
  onLoad() {
    // 获取系统高度信息
    this.systemHeights = this.$getSystemHeights()
    
    // 等待tabbar高度获取完成
    this.$waitForTabbarHeight((height) => {
      this.tabbarHeight = height
      this.contentHeight = this.$getContentHeight(height)
    })
  },
  
  methods: {
    // 直接获取当前高度
    getHeightDirectly() {
      const height = this.$getTabbarHeight()
      uni.showToast({
        title: height > 0 ? `高度: ${height}px` : '高度未获取到',
        icon: 'none'
      })
    },
    
    // 从全局变量获取
    getHeightFromGlobal() {
      const heights = []
      
      // 从uni.tabbarHeight获取
      if (uni.tabbarHeight) {
        heights.push(`uni.tabbarHeight: ${uni.tabbarHeight}px`)
      }
      
      // 从globalData获取
      const app = getApp()
      if (app.globalData && app.globalData.tabbarHeight) {
        heights.push(`globalData: ${app.globalData.tabbarHeight}px`)
      }
      
      // 从uni.$u获取
      if (uni.$u && uni.$u.tabbarHeight) {
        heights.push(`uni.$u: ${uni.$u.tabbarHeight}px`)
      }
      
      uni.showModal({
        title: '全局高度信息',
        content: heights.length > 0 ? heights.join('\n') : '未找到高度信息',
        showCancel: false
      })
    },
    
    // 测量自定义元素高度
    measureCustomElement() {
      this.$getElementHeight('.test-element', this, (height) => {
        this.testElementHeight = height
        uni.showToast({
          title: `测试元素高度: ${height}px`,
          icon: 'none'
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.page {
  min-height: 100vh;
  background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
}

.content {
  padding: 30rpx;
  
  .demo-section {
    .section-title {
      display: block;
      font-size: 32rpx;
      font-weight: bold;
      color: white;
      text-align: center;
      margin-bottom: 40rpx;
    }
    
    .info-cards {
      display: flex;
      flex-direction: column;
      gap: 20rpx;
      margin-bottom: 40rpx;
      
      .info-card {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border-radius: 16rpx;
        padding: 30rpx;
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .card-label {
          font-size: 28rpx;
          color: rgba(255, 255, 255, 0.8);
        }
        
        .card-value {
          font-size: 28rpx;
          color: white;
          font-weight: bold;
        }
      }
    }
    
    .buttons {
      display: flex;
      flex-direction: column;
      gap: 20rpx;
      margin-bottom: 40rpx;
    }
    
    .test-element {
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(10px);
      border-radius: 16rpx;
      padding: 40rpx;
      margin-bottom: 40rpx;
      
      .test-text {
        font-size: 28rpx;
        color: white;
        text-align: center;
        line-height: 1.6;
      }
    }
    
    .usage-tips {
      background: rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(10px);
      border-radius: 16rpx;
      padding: 30rpx;
      
      .tips-title {
        display: block;
        font-size: 30rpx;
        font-weight: bold;
        color: white;
        margin-bottom: 20rpx;
      }
      
      .tips-item {
        display: block;
        font-size: 26rpx;
        color: rgba(255, 255, 255, 0.9);
        line-height: 1.6;
        margin-bottom: 10rpx;
      }
    }
  }
}
</style>
