<template>
	<view class="page">
		<!-- 内容区域 -->
		<view class="content" :style="{ paddingBottom: tabbarHeight + 'px' }">
			<view class="header">
				<text class="title">职你破默</text>
				<text class="subtitle">智能求职平台</text>
			</view>

			<view class="info-section">
				<view class="info-item">
					<text class="label">Tabbar高度:</text>
					<text class="value">{{ tabbarHeight }}px</text>
				</view>
				<view class="info-item">
					<text class="label">内容区域高度:</text>
					<text class="value">{{ contentHeight }}px</text>
				</view>
			</view>

			<view class="demo-content">
				<text class="demo-text">这是主页内容，底部padding根据tabbar高度自动调整</text>
			</view>
		</view>

		<!-- 公共TabBar -->
		<custom-tabbar :value="0" />
	</view>
</template>

<script>
import CustomTabbar from '@/components/CustomTabbar.vue'

export default {
	components: { CustomTabbar },
	data() {
		return {
			tabbarHeight: 0,
			contentHeight: 0
		}
	},

	onLoad() {
		// 页面加载时获取tabbar高度
		this.getTabbarHeight()
	},

	methods: {
		// 获取tabbar高度
		getTabbarHeight() {
			// 使用全局的tabbarManager获取高度
			this.$tabbarManager.getHeight((height) => {
				this.tabbarHeight = height
				// 计算内容区域高度
				this.contentHeight = this.$tabbarManager.constructor.getContentHeight(height)
				console.log('首页获取到tabbar高度:', height)
			})
		}
	}
}
</script>

<style lang="scss" scoped>
.page {
	min-height: 100vh;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.content {
	padding: 40rpx 30rpx;

	.header {
		text-align: center;
		margin-bottom: 60rpx;

		.title {
			display: block;
			font-size: 48rpx;
			font-weight: bold;
			color: white;
			margin-bottom: 20rpx;
		}

		.subtitle {
			font-size: 28rpx;
			color: rgba(255, 255, 255, 0.8);
		}
	}

	.info-section {
		background: rgba(255, 255, 255, 0.1);
		border-radius: 20rpx;
		padding: 30rpx;
		margin-bottom: 40rpx;
		backdrop-filter: blur(10px);

		.info-item {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 20rpx;

			&:last-child {
				margin-bottom: 0;
			}

			.label {
				font-size: 28rpx;
				color: rgba(255, 255, 255, 0.8);
			}

			.value {
				font-size: 28rpx;
				color: white;
				font-weight: bold;
			}
		}
	}

	.demo-content {
		background: rgba(255, 255, 255, 0.1);
		border-radius: 20rpx;
		padding: 40rpx;
		backdrop-filter: blur(10px);

		.demo-text {
			font-size: 30rpx;
			color: white;
			text-align: center;
			line-height: 1.6;
		}
	}
}
</style>