<template>
  <view class="page">
    <view class="test-container">
      <view class="test-header">
        <text class="test-title">Tabbar高度测试页面</text>
      </view>
      
      <view class="test-results">
        <view class="result-item">
          <text class="result-label">通过$tabbarManager获取:</text>
          <text class="result-value">{{ heightFromManager }}px</text>
        </view>
        
        <view class="result-item">
          <text class="result-label">通过uni.tabbarManager获取:</text>
          <text class="result-value">{{ heightFromUni }}px</text>
        </view>
        
        <view class="result-item">
          <text class="result-label">通过globalData获取:</text>
          <text class="result-value">{{ heightFromGlobalData }}px</text>
        </view>
        
        <view class="result-item">
          <text class="result-label">通过uni.$u获取:</text>
          <text class="result-value">{{ heightFromUniU }}px</text>
        </view>
        
        <view class="result-item">
          <text class="result-label">高度是否已准备好:</text>
          <text class="result-value">{{ isReady ? '是' : '否' }}</text>
        </view>
      </view>
      
      <view class="test-buttons">
        <u-button type="primary" @click="testAllMethods">测试所有获取方法</u-button>
        <u-button type="success" @click="testAsyncGet">测试异步获取</u-button>
        <u-button type="warning" @click="testSyncGet">测试同步获取</u-button>
      </view>
      
      <view class="log-section">
        <text class="log-title">测试日志:</text>
        <view class="log-content">
          <text v-for="(log, index) in logs" :key="index" class="log-item">{{ log }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'TabbarTest',
  data() {
    return {
      heightFromManager: 0,
      heightFromUni: 0,
      heightFromGlobalData: 0,
      heightFromUniU: 0,
      isReady: false,
      logs: []
    }
  },
  
  onLoad() {
    this.addLog('页面加载完成')
    this.testAllMethods()
  },
  
  methods: {
    // 添加日志
    addLog(message) {
      const time = new Date().toLocaleTimeString()
      this.logs.unshift(`[${time}] ${message}`)
      if (this.logs.length > 10) {
        this.logs.pop()
      }
    },
    
    // 测试所有获取方法
    testAllMethods() {
      this.addLog('开始测试所有获取方法')
      
      // 测试$tabbarManager
      if (this.$tabbarManager) {
        this.heightFromManager = this.$tabbarManager.getHeightSync()
        this.isReady = this.$tabbarManager.isHeightReady()
        this.addLog(`$tabbarManager获取: ${this.heightFromManager}px`)
      } else {
        this.addLog('$tabbarManager未找到')
      }
      
      // 测试uni.tabbarManager
      if (uni.tabbarManager) {
        this.heightFromUni = uni.tabbarManager.getHeightSync()
        this.addLog(`uni.tabbarManager获取: ${this.heightFromUni}px`)
      } else {
        this.addLog('uni.tabbarManager未找到')
      }
      
      // 测试globalData
      const app = getApp()
      if (app.globalData && app.globalData.tabbarHeight) {
        this.heightFromGlobalData = app.globalData.tabbarHeight
        this.addLog(`globalData获取: ${this.heightFromGlobalData}px`)
      } else {
        this.addLog('globalData中未找到tabbarHeight')
      }
      
      // 测试uni.$u
      if (uni.$u && uni.$u.tabbarHeight) {
        this.heightFromUniU = uni.$u.tabbarHeight
        this.addLog(`uni.$u获取: ${this.heightFromUniU}px`)
      } else {
        this.addLog('uni.$u中未找到tabbarHeight')
      }
    },
    
    // 测试异步获取
    testAsyncGet() {
      this.addLog('开始异步获取测试')
      if (this.$tabbarManager) {
        this.$tabbarManager.getHeight((height) => {
          this.addLog(`异步获取成功: ${height}px`)
          uni.showToast({
            title: `异步获取: ${height}px`,
            icon: 'none'
          })
        })
      }
    },
    
    // 测试同步获取
    testSyncGet() {
      this.addLog('开始同步获取测试')
      if (this.$tabbarManager) {
        const height = this.$tabbarManager.getHeightSync()
        this.addLog(`同步获取结果: ${height}px`)
        uni.showToast({
          title: height > 0 ? `同步获取: ${height}px` : '高度未准备好',
          icon: 'none'
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20rpx;
}

.test-container {
  .test-header {
    text-align: center;
    margin-bottom: 40rpx;
    
    .test-title {
      font-size: 36rpx;
      font-weight: bold;
      color: #333;
    }
  }
  
  .test-results {
    background: white;
    border-radius: 16rpx;
    padding: 30rpx;
    margin-bottom: 30rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
    
    .result-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20rpx 0;
      border-bottom: 1rpx solid #eee;
      
      &:last-child {
        border-bottom: none;
      }
      
      .result-label {
        font-size: 28rpx;
        color: #666;
      }
      
      .result-value {
        font-size: 28rpx;
        color: #333;
        font-weight: bold;
      }
    }
  }
  
  .test-buttons {
    display: flex;
    flex-direction: column;
    gap: 20rpx;
    margin-bottom: 30rpx;
  }
  
  .log-section {
    background: white;
    border-radius: 16rpx;
    padding: 30rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
    
    .log-title {
      font-size: 30rpx;
      font-weight: bold;
      color: #333;
      margin-bottom: 20rpx;
    }
    
    .log-content {
      max-height: 400rpx;
      overflow-y: auto;
      
      .log-item {
        display: block;
        font-size: 24rpx;
        color: #666;
        line-height: 1.5;
        margin-bottom: 10rpx;
        padding: 10rpx;
        background: #f8f8f8;
        border-radius: 8rpx;
      }
    }
  }
}
</style>
