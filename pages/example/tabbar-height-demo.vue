<template>
  <view class="page">
    <view class="header">
      <text class="title">Tabbar高度使用示例</text>
    </view>
    
    <view class="content">
      <view class="info-card">
        <text class="label">Tabbar高度:</text>
        <text class="value">{{ tabbarHeight }}px</text>
      </view>
      
      <view class="info-card">
        <text class="label">高度状态:</text>
        <text class="value">{{ heightStatus }}</text>
      </view>
      
      <view class="info-card">
        <text class="label">内容区域高度:</text>
        <text class="value">{{ contentHeight }}px</text>
      </view>
      
      <view class="buttons">
        <u-button type="primary" @click="getHeightSync">同步获取高度</u-button>
        <u-button type="success" @click="getHeightAsync">异步获取高度</u-button>
        <u-button type="warning" @click="calculateContentHeight">计算内容高度</u-button>
      </view>
      
      <view class="demo-area" :style="{ height: demoAreaHeight + 'px' }">
        <text class="demo-text">这是一个演示区域，高度根据tabbar高度动态计算</text>
      </view>
    </view>
  </view>
</template>

<script>
import tabbarManager from '@/utils/tabbar.js'

export default {
  name: 'TabbarHeightDemo',
  data() {
    return {
      tabbarHeight: 0,
      heightStatus: '未获取',
      contentHeight: 0,
      demoAreaHeight: 200
    }
  },
  
  onLoad() {
    // 页面加载时尝试获取高度
    this.initHeight()
  },
  
  methods: {
    // 初始化高度
    initHeight() {
      // 异步获取高度，如果高度还没准备好会等待
      tabbarManager.getHeight((height) => {
        this.tabbarHeight = height
        this.heightStatus = '已获取'
        this.calculateContentHeight()
      })
    },
    
    // 同步获取高度
    getHeightSync() {
      const height = tabbarManager.getHeightSync()
      if (height > 0) {
        this.tabbarHeight = height
        this.heightStatus = '同步获取成功'
        uni.showToast({
          title: `高度: ${height}px`,
          icon: 'none'
        })
      } else {
        uni.showToast({
          title: '高度还未准备好',
          icon: 'none'
        })
      }
    },
    
    // 异步获取高度
    getHeightAsync() {
      tabbarManager.getHeight((height) => {
        this.tabbarHeight = height
        this.heightStatus = '异步获取成功'
        uni.showToast({
          title: `高度: ${height}px`,
          icon: 'none'
        })
      })
    },
    
    // 计算内容区域高度
    calculateContentHeight() {
      if (this.tabbarHeight > 0) {
        // 使用TabbarManager的静态方法计算内容高度
        this.contentHeight = tabbarManager.constructor.getContentHeight(this.tabbarHeight)
        // 设置演示区域高度为内容高度的一半
        this.demoAreaHeight = this.contentHeight / 2
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.page {
  padding: 20rpx;
  min-height: 100vh;
  background-color: #f5f5f5;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
  
  .title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
  }
}

.content {
  .info-card {
    background: white;
    padding: 30rpx;
    margin-bottom: 20rpx;
    border-radius: 16rpx;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
    
    .label {
      font-size: 28rpx;
      color: #666;
    }
    
    .value {
      font-size: 28rpx;
      color: #333;
      font-weight: bold;
    }
  }
  
  .buttons {
    margin: 40rpx 0;
    display: flex;
    flex-direction: column;
    gap: 20rpx;
  }
  
  .demo-area {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 16rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 40rpx;
    
    .demo-text {
      color: white;
      font-size: 28rpx;
      text-align: center;
      padding: 20rpx;
    }
  }
}
</style>
