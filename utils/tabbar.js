/**
 * Tabbar高度管理工具类
 * 用于获取和管理CustomTabbar的高度，供全局使用
 */

class TabbarManager {
  constructor() {
    this.height = 0
    this.isReady = false
    this.callbacks = []
  }

  /**
   * 设置tabbar高度
   * @param {number} height - tabbar高度
   */
  setHeight(height) {
    this.height = height
    this.isReady = true
    
    // 存储到全局变量
    getApp().globalData = getApp().globalData || {}
    getApp().globalData.tabbarHeight = height
    
    // 存储到uni.$u
    if (uni.$u) {
      uni.$u.tabbarHeight = height
    }
    
    // 执行所有等待的回调
    this.callbacks.forEach(callback => {
      try {
        callback(height)
      } catch (error) {
        console.error('TabbarManager callback error:', error)
      }
    })
    
    // 清空回调数组
    this.callbacks = []
    
    console.log('TabbarManager: 高度已设置为', height)
  }

  /**
   * 获取tabbar高度
   * @param {function} callback - 获取高度后的回调函数
   * @returns {number|void} - 如果高度已准备好则直接返回，否则通过回调返回
   */
  getHeight(callback) {
    if (this.isReady) {
      if (callback) {
        callback(this.height)
      }
      return this.height
    } else {
      if (callback) {
        this.callbacks.push(callback)
      }
      return null
    }
  }

  /**
   * 同步获取tabbar高度（如果已准备好）
   * @returns {number} - tabbar高度，如果未准备好返回0
   */
  getHeightSync() {
    return this.isReady ? this.height : 0
  }

  /**
   * 检查高度是否已准备好
   * @returns {boolean}
   */
  isHeightReady() {
    return this.isReady
  }

  /**
   * 重置状态
   */
  reset() {
    this.height = 0
    this.isReady = false
    this.callbacks = []
  }

  /**
   * 使用uni.createSelectorQuery获取指定组件的高度
   * @param {string} selector - 选择器
   * @param {object} context - 组件上下文，通常是this
   * @param {function} callback - 回调函数
   */
  static measureHeight(selector, context, callback) {
    const query = uni.createSelectorQuery().in(context)
    query.select(selector).boundingClientRect((data) => {
      if (data && callback) {
        callback(data.height)
      }
    }).exec()
  }

  /**
   * 获取系统状态栏高度
   * @returns {number}
   */
  static getStatusBarHeight() {
    const systemInfo = uni.getSystemInfoSync()
    return systemInfo.statusBarHeight || 0
  }

  /**
   * 获取系统安全区域底部高度
   * @returns {number}
   */
  static getSafeAreaBottom() {
    const systemInfo = uni.getSystemInfoSync()
    const safeArea = systemInfo.safeArea
    if (safeArea) {
      return systemInfo.screenHeight - safeArea.bottom
    }
    return 0
  }

  /**
   * 计算页面内容区域的可用高度
   * @param {number} tabbarHeight - tabbar高度
   * @param {number} navbarHeight - 导航栏高度（可选）
   * @returns {number}
   */
  static getContentHeight(tabbarHeight, navbarHeight = 0) {
    const systemInfo = uni.getSystemInfoSync()
    const windowHeight = systemInfo.windowHeight
    return windowHeight - tabbarHeight - navbarHeight
  }
}

// 创建全局实例
const tabbarManager = new TabbarManager()

// 挂载到uni对象上
uni.tabbarManager = tabbarManager

// 导出实例和类
export default tabbarManager
export { TabbarManager }
