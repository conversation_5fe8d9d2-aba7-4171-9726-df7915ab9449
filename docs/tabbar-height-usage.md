# CustomTabbar高度获取和全局使用指南

## 概述

本项目实现了CustomTabbar高度的自动获取和全局管理功能，通过`uni.createSelectorQuery()`获取tabbar的实际高度，并提供了便捷的API供各个页面使用。

## 核心文件

### 1. `utils/tabbar.js` - Tabbar高度管理工具类
- 提供TabbarManager类用于管理tabbar高度
- 支持同步和异步获取高度
- 提供回调机制，确保高度获取后执行相关操作
- 包含辅助方法计算内容区域高度

### 2. `components/CustomTabbar.vue` - 自定义Tabbar组件
- 在组件挂载后自动获取高度
- 使用TabbarManager设置全局高度
- 保持原有的导航功能

## 使用方法

### 1. 基本使用

```javascript
// 在页面中引入
import tabbarManager from '@/utils/tabbar.js'

// 或者使用全局挂载的实例
this.$tabbarManager
// 或者
uni.tabbarManager
```

### 2. 获取Tabbar高度

#### 同步获取（如果高度已准备好）
```javascript
const height = tabbarManager.getHeightSync()
if (height > 0) {
  console.log('Tabbar高度:', height)
} else {
  console.log('高度还未准备好')
}
```

#### 异步获取（推荐）
```javascript
tabbarManager.getHeight((height) => {
  console.log('Tabbar高度:', height)
  // 在这里处理获取到高度后的逻辑
})
```

#### 检查高度是否已准备好
```javascript
if (tabbarManager.isHeightReady()) {
  const height = tabbarManager.getHeightSync()
  // 处理高度
}
```

### 3. 计算内容区域高度

```javascript
// 获取可用的内容区域高度
const tabbarHeight = tabbarManager.getHeightSync()
const contentHeight = tabbarManager.constructor.getContentHeight(tabbarHeight)
console.log('内容区域高度:', contentHeight)
```

### 4. 在页面中使用

```javascript
export default {
  data() {
    return {
      tabbarHeight: 0,
      contentHeight: 0
    }
  },
  
  onLoad() {
    // 页面加载时获取高度
    this.$tabbarManager.getHeight((height) => {
      this.tabbarHeight = height
      this.contentHeight = this.$tabbarManager.constructor.getContentHeight(height)
    })
  },
  
  methods: {
    // 根据tabbar高度调整页面布局
    adjustLayout() {
      const height = this.$tabbarManager.getHeightSync()
      if (height > 0) {
        // 调整页面元素的高度或位置
        this.someElementHeight = `calc(100vh - ${height}px)`
      }
    }
  }
}
```

### 5. 在CSS中使用

```scss
// 可以通过计算属性或data属性将高度传递给CSS
.content-area {
  height: calc(100vh - var(--tabbar-height));
}

// 或者在组件中动态设置
.content-area {
  height: calc(100vh - 50px); // 假设tabbar高度为50px
}
```

## API参考

### TabbarManager类方法

#### `setHeight(height)`
设置tabbar高度
- `height` {number} - tabbar高度（像素）

#### `getHeight(callback)`
获取tabbar高度
- `callback` {function} - 获取高度后的回调函数
- 返回值: {number|null} - 如果高度已准备好则直接返回，否则返回null

#### `getHeightSync()`
同步获取tabbar高度
- 返回值: {number} - tabbar高度，如果未准备好返回0

#### `isHeightReady()`
检查高度是否已准备好
- 返回值: {boolean} - 是否已准备好

#### `reset()`
重置状态

### 静态方法

#### `TabbarManager.measureHeight(selector, context, callback)`
使用uni.createSelectorQuery获取指定组件的高度
- `selector` {string} - 选择器
- `context` {object} - 组件上下文
- `callback` {function} - 回调函数

#### `TabbarManager.getStatusBarHeight()`
获取系统状态栏高度
- 返回值: {number} - 状态栏高度

#### `TabbarManager.getSafeAreaBottom()`
获取系统安全区域底部高度
- 返回值: {number} - 安全区域底部高度

#### `TabbarManager.getContentHeight(tabbarHeight, navbarHeight)`
计算页面内容区域的可用高度
- `tabbarHeight` {number} - tabbar高度
- `navbarHeight` {number} - 导航栏高度（可选，默认0）
- 返回值: {number} - 内容区域高度

## 全局访问方式

高度信息会同时存储在以下位置：

1. `getApp().globalData.tabbarHeight` - 应用全局数据
2. `uni.$u.tabbarHeight` - uView框架全局对象
3. `uni.tabbarManager` - TabbarManager实例
4. `this.$tabbarManager` - Vue组件实例（需要在main.js中挂载）

## 注意事项

1. 高度获取是异步的，建议使用回调方式获取
2. 在页面onLoad或mounted生命周期中获取高度
3. 如果需要在高度获取前就使用，建议设置默认值
4. 高度会在CustomTabbar组件挂载后自动获取和设置

## 示例页面

参考 `pages/example/tabbar-height-demo.vue` 查看完整的使用示例。
