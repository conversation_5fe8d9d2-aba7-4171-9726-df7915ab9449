import App from './App'

// #ifndef VUE3
import Vue from 'vue'
import './uni.promisify.adaptor'
Vue.config.productionTip = false
App.mpType = 'app'
const app = new Vue({
  ...App
})
app.$mount()
// #endif
// main.js
import uView from "uview-ui";
Vue.use(uView);

// 引入tabbar高度获取工具函数
import { getTabbarHeight, getElementHeight, getSystemHeights, getContentHeight, waitForTabbarHeight } from '@/utils/tabbar.js'

// 挂载到Vue原型上，方便在组件中使用
Vue.prototype.$getTabbarHeight = getTabbarHeight
Vue.prototype.$getElementHeight = getElementHeight
Vue.prototype.$getSystemHeights = getSystemHeights
Vue.prototype.$getContentHeight = getContentHeight
Vue.prototype.$waitForTabbarHeight = waitForTabbarHeight

// #ifdef VUE3
import { createSSRApp } from 'vue'
export function createApp() {
  const app = createSSRApp(App)
  return {
    app
  }
}
// #endif